import { Middleware } from '@reduxjs/toolkit'

import { KEY_TOKEN } from '../../constants'
import { appCookie, clearUserAnalyticsId } from '../../utils'
import { clearUserAuthToken, setUserAuthToken } from '..'

import { RootState } from './../store'

/**
 * user 操作相关中间件
 */
const userMiddleware: Middleware<unknown, RootState> = (store) => (next) => (action) => {
  const result = next(action)

  // 写入 token 时，将 token 写入到 cookie
  if (setUserAuthToken.match(action)) {
    const { authToken } = store.getState().user
    appCookie.setItem(KEY_TOKEN, authToken)
  }

  // token 删除时，从 storage 中删除 token 并清除埋点用户标识
  if (clearUserAuthToken.match(action)) {
    appCookie.removeItem(KEY_TOKEN)
    // 清除埋点用户标识
    clearUserAnalyticsId()
  }

  return result
}

export default userMiddleware
